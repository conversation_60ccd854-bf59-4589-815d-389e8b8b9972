{"id": "a951c344-01ce-46dc-b07b-a1d4392a433c", "name": "comparison-development-hierarchical", "description": "Performance comparison benchmark", "status": "completed", "config": {"name": "comparison-development-hierarchical", "description": "Performance comparison benchmark", "strategy": "development", "mode": "hierarchical", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 30, "max_retries": 2, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "c619a539-5a99-44ef-a3b7-1a4d89f8e5fd", "objective": "Build a REST API for user management", "description": "Benchmark task: Build a REST API for user management", "strategy": "development", "mode": "hierarchical", "parameters": {}, "timeout": 30, "max_retries": 2, "priority": 1, "status": "pending", "created_at": "2025-06-14T20:02:32.273430", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "43a65f60-12bd-4373-93aa-29e601efe145", "task_id": "c619a539-5a99-44ef-a3b7-1a4d89f8e5fd", "agent_id": "development-agent", "status": "success", "output": {"code_implementation": "Code implementation completed for: Build a REST API for user management", "files_created": ["main.py", "utils.py", "tests.py"], "lines_of_code": 250, "test_coverage": 0.95, "code_quality": 0.9}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.200346, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 25.0, "memory_mb": 256, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T20:02:32.473896", "started_at": "2025-06-14T20:02:32.273483", "completed_at": "2025-06-14T20:02:32.473856", "duration": 0.200373}], "metrics": {"total_tasks": 1, "completed_tasks": 0, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.200346, "total_execution_time": 0.200346, "success_rate": 0.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T20:02:32.273458", "started_at": "2025-06-14T20:02:32.273464", "completed_at": "2025-06-14T20:02:32.473926", "duration": 0.200462, "error_log": [], "metadata": {}}