# Docker environment for testing npm install claude-flow@2.0.0
FROM node:20-alpine

# Install dependencies
RUN apk add --no-cache git bash curl jq

# Create test directory
WORKDIR /test

# Install claude-flow locally via npm
RUN npm init -y
RUN npm install claude-flow@2.0.0

# Create test scripts directory
RUN mkdir -p /test/scripts /test/results /test/logs

# Copy test script
COPY test-mcp-tools.sh /test/scripts/
RUN chmod +x /test/scripts/test-mcp-tools.sh

# Default command
CMD ["/test/scripts/test-mcp-tools.sh", "npm"]