# Docker Compose override for local development
# This file allows developers to customize the environment without modifying the main compose file

version: '3.8'

services:
  claude-flow-dev:
    # Override for local development
    volumes:
      - ../:/app:cached  # Use cached mounting for better performance on macOS
    environment:
      - DEBUG=claude-flow:*,ruv-swarm:*
      - LOG_LEVEL=debug
      - DEVELOPMENT_MODE=true
    ports:
      - "3000:3000"
      - "3001:3001"
      - "8080:8080"
      - "9229:9229"  # Node.js debugging port

  ruv-swarm-integration:
    # Override for swarm development
    environment:
      - SWARM_DEBUG=true
      - SWARM_LOG_LEVEL=debug
      - MCP_DEBUG=true
    volumes:
      - ../../ruv-swarm:/app/ruv-swarm:cached

  # Additional development services
  redis:
    image: redis:alpine
    container_name: claude-flow-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - claude-swarm-network

  # Database admin interface
  db-admin:
    image: sqlitebrowser/sqlitebrowser:latest
    container_name: claude-flow-db-admin
    ports:
      - "8081:8080"
    volumes:
      - claude-flow-db-data:/data
    networks:
      - claude-swarm-network

volumes:
  redis-data:
    driver: local