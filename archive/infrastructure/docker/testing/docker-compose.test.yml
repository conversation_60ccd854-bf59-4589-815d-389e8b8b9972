# Docker Compose configuration for testing
# Optimized for CI/CD and automated testing environments

version: '3.8'

services:
  claude-flow-test:
    build:
      context: ../../..
      dockerfile: infrastructure/docker/testing/Dockerfile
      target: testing
    container_name: claude-flow-test
    environment:
      - NODE_ENV=test
      - CI=true
      - FORCE_COLOR=1
      - JEST_JUNIT_OUTPUT_DIR=/app/test-results
      - JEST_JUNIT_OUTPUT_NAME=junit.xml
    volumes:
      - claude-flow-test-results:/app/test-results
      - claude-flow-coverage:/app/coverage
    depends_on:
      - test-db
    networks:
      - claude-test-network

  test-db:
    image: sqlite:latest
    container_name: claude-flow-test-db
    environment:
      - SQLITE_DATABASE=test.db
    volumes:
      - test-db-data:/var/lib/sqlite
    networks:
      - claude-test-network

  # Integration test runner
  integration-test:
    build:
      context: ../../..
      dockerfile: infrastructure/docker/testing/Dockerfile
      target: testing
    container_name: claude-flow-integration-test
    environment:
      - NODE_ENV=test
      - TEST_TYPE=integration
      - CI=true
    volumes:
      - claude-flow-test-results:/app/test-results
    depends_on:
      - claude-flow-test
      - test-db
    networks:
      - claude-test-network
    command: ["npm", "run", "test:integration"]

  # E2E test runner
  e2e-test:
    build:
      context: ../../..
      dockerfile: infrastructure/docker/testing/Dockerfile
      target: testing
    container_name: claude-flow-e2e-test
    environment:
      - NODE_ENV=test
      - TEST_TYPE=e2e
      - CI=true
      - HEADLESS=true
    volumes:
      - claude-flow-test-results:/app/test-results
      - claude-flow-coverage:/app/coverage
    depends_on:
      - claude-flow-test
      - test-db
    networks:
      - claude-test-network
    command: ["npm", "run", "test:e2e"]

  # Performance test runner
  performance-test:
    build:
      context: ../../..
      dockerfile: infrastructure/docker/testing/Dockerfile
      target: testing
    container_name: claude-flow-performance-test
    environment:
      - NODE_ENV=test
      - TEST_TYPE=performance
      - CI=true
    volumes:
      - claude-flow-test-results:/app/test-results
    depends_on:
      - claude-flow-test
      - test-db
    networks:
      - claude-test-network
    command: ["npm", "run", "test:performance"]

  # Test result aggregator
  test-aggregator:
    image: node:20-alpine
    container_name: claude-flow-test-aggregator
    working_dir: /app
    environment:
      - NODE_ENV=test
    volumes:
      - claude-flow-test-results:/app/test-results
      - ./scripts:/app/scripts
    networks:
      - claude-test-network
    command: ["node", "/app/scripts/aggregate-test-results.js"]
    depends_on:
      - claude-flow-test
      - integration-test
      - e2e-test
      - performance-test

networks:
  claude-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  claude-flow-test-results:
    driver: local
  claude-flow-coverage:
    driver: local
  test-db-data:
    driver: local