version: '3.8'

services:
  # NPX installation testing
  test-npx-swarm:
    build:
      context: .
      dockerfile: Dockerfile.npx
    container_name: claude-flow-test-npx-swarm
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npx", "swarm"]
    networks:
      - claude-flow-test

  test-npx-neural:
    build:
      context: .
      dockerfile: Dockerfile.npx
    container_name: claude-flow-test-npx-neural
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npx", "neural"]
    networks:
      - claude-flow-test

  test-npx-memory:
    build:
      context: .
      dockerfile: Dockerfile.npx
    container_name: claude-flow-test-npx-memory
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npx", "memory"]
    networks:
      - claude-flow-test

  test-npx-analysis:
    build:
      context: .
      dockerfile: Dockerfile.npx
    container_name: claude-flow-test-npx-analysis
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npx", "analysis"]
    networks:
      - claude-flow-test

  # NPM installation testing
  test-npm-workflow:
    build:
      context: .
      dockerfile: Dockerfile.npm
    container_name: claude-flow-test-npm-workflow
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npm", "workflow"]
    networks:
      - claude-flow-test

  test-npm-github:
    build:
      context: .
      dockerfile: Dockerfile.npm
    container_name: claude-flow-test-npm-github
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npm", "github"]
    networks:
      - claude-flow-test

  test-npm-daa:
    build:
      context: .
      dockerfile: Dockerfile.npm
    container_name: claude-flow-test-npm-daa
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npm", "daa"]
    networks:
      - claude-flow-test

  test-npm-system:
    build:
      context: .
      dockerfile: Dockerfile.npm
    container_name: claude-flow-test-npm-system
    volumes:
      - ./results:/test/results
      - ./logs:/test/logs
    command: ["/test/scripts/test-mcp-tools.sh", "npm", "system"]
    networks:
      - claude-flow-test

networks:
  claude-flow-test:
    driver: bridge