{"default": [{"key": "key18", "value": "value18", "namespace": "default", "timestamp": 1751683807565}, {"key": "research/neural-interfaces/key-findings", "value": "NEURAL INTERFACE KEY FINDINGS: NON-INVASIVE: EEG (1-5ms temporal, 5-10cm spatial, 9-50k+), fNIRS (blood oxygenation, 1-3cm spatial), MEG (<1ms temporal, 2-5mm spatial, -3M), Transcranial stim (TMS/tDCS/tACS). INVASIVE: Utah Arrays (96-100 electrodes, FDA approved), Michigan Probes (less damage, customizable), Neuralink-style (1024+ flexible electrodes, wireless), ECoG (surface arrays). SIGNAL PROCESSING: CSP (80-90% motor imagery), ICA (artifact removal), Deep Learning (EEGNet, Transformers, 70-95% accuracy). FRAMEWORKS: OpenBCI, BCI2000, MNE-Python, EEGLAB/BCILAB. SAFETY: FDA Class II/III, ISO 14708, ISO 10993, Pt/Ir electrodes. FUTURE: 10k+ channels, bidirectional, edge AI, biodegradable.", "namespace": "default", "timestamp": 1752018127114}, {"key": "research/neural-interfaces/commercial-systems", "value": "COMMERCIAL BCI SYSTEMS: Consumer: Emotiv EPOC X (49, 14ch), NeuroSky MindWave (9, 1ch), Muse headband (49, 4ch). Research: g.tec g.USBamp (256ch), Brain Products actiCHamp Plus (160ch), NIRx NIRSport2 (64ch fNIRS), Kernel Flow (full-head fNIRS). Clinical: Utah Array/BrainGate (FDA approved), NeuroPace RNS (epilepsy), Medtronic DBS. High-end: MEGIN TRIUX neo (306ch MEG), CTF MEG (275ch). Development: OpenBC<PERSON> Cyton (8ch, 99), <PERSON><PERSON> (4ch, 99).", "namespace": "default", "timestamp": 1752018139320}, {"key": "research/neural-interfaces/performance-benchmarks", "value": "BCI PERFORMANCE BENCHMARKS: Motor Imagery 4-class: 70-80% accuracy. P300 Speller: 90-95% accuracy. Emotion Recognition: 60-75% accuracy. Sleep Stage Classification: 80-85% accuracy. Single neuron decoding: 95%+ (invasive). Speech decoding: 70-80 WPM (invasive). Cursor control: 6-8 targets/min (invasive), 2-4 targets/min (non-invasive). Information transfer rates: 1-3 bits/s (non-invasive), 3-5 bits/s (invasive). Training time: 10-30 min calibration typical.", "namespace": "default", "timestamp": 1752018149865}, {"key": "safety/critical-limits", "value": "{\"electrical\":{\"current_density_max_uA_mm2\":2.0,\"voltage_dc_noninvasive_V\":5.0,\"voltage_dc_invasive_V\":0.5,\"charge_injection_max_uC_cm2\":30.0,\"pulse_duration_max_ms\":1.0,\"leakage_current_max_uA\":10.0,\"isolation_voltage_kV\":4.0},\"thermal\":{\"temperature_rise_max_C\":2.0,\"absolute_temp_max_C\":41.0,\"power_density_max_mW_cm2\":40.0,\"thermal_shutdown_C\":41.0},\"temporal\":{\"duty_cycle_max_percent\":50,\"session_duration_max_min\":120,\"break_interval_min\":30,\"watchdog_timeout_ms\":100},\"cognitive\":{\"information_rate_max_bps\":100,\"frequency_sensory_max_Hz\":1000,\"frequency_motor_max_Hz\":100,\"frequency_cognitive_max_Hz\":40},\"emergency\":{\"estop_response_ms\":10,\"power_removal_ms\":50,\"disconnect_time_ms\":50,\"alert_time_ms\":1000},\"safety_factors\":{\"current_safety_factor\":1.1,\"voltage_safety_factor\":1.05,\"temperature_margin_C\":2.0}}", "namespace": "default", "timestamp": 1752018150613}, {"key": "research/neural-interfaces/safety-regulations", "value": "BCI SAFETY REGULATIONS: FDA: Class II non-invasive (510k), Class III invasive (PMA). ISO 14708: Active implantable devices. ISO 10993: Biocompatibility (cytotoxicity, sensitization, irritation, systemic toxicity, genotoxicity, implantation). IEC 60601: Electrical safety. IEC 62304: Software validation. Materials: Platinum/Iridium electrodes, Parylene-C insulation, medical silicone, titanium packaging. Clinical trials required for Class III. MRI compatibility testing. EMC testing. Hermetic sealing for implants.", "namespace": "default", "timestamp": 1752018160824}, {"key": "safety/emergency-protocols", "value": "{\"immediate_disconnect\":{\"user_initiated\":[\"physical_estop\",\"voice_command\",\"triple_tap\"],\"system_initiated\":[\"safety_violation\",\"power_loss\",\"component_failure\"],\"response_time_ms\":10},\"medical_response\":{\"auto_alert\":true,\"telemedicine_enabled\":true,\"first_responder_access\":true,\"transport_mode\":\"safe_state\"},\"panic_response\":{\"triggers\":[\"physical_button\",\"voice_help\",\"software_button\"],\"actions\":[\"immediate_disconnect\",\"alert_contacts\",\"log_state\",\"recovery_mode\"]}}", "namespace": "default", "timestamp": 1752018161838}, {"key": "safety/compliance-requirements", "value": "{\"standards\":{\"FDA\":\"Class_II_III\",\"CE\":\"MDR_compliant\",\"ISO\":[\"14708\",\"10993\",\"14971\",\"14155\"],\"IEC\":[\"60601-1\",\"60601-1-2\",\"62304\"]},\"testing\":{\"electrical_safety\":\"IEC_60601-1\",\"EMC\":\"IEC_60601-1-2\",\"biocompatibility\":\"ISO_10993_series\",\"software\":\"IEC_62304\"},\"documentation\":{\"DHF\":\"required\",\"risk_management\":\"ISO_14971\",\"clinical_evaluation\":\"ongoing\",\"post_market_surveillance\":\"active\"}}", "namespace": "default", "timestamp": 1752018174195}, {"key": "design/architecture/decisions", "value": "Architecture decisions for Neural Link Hive Integration: 1) Microservice architecture for scalability and modularity. 2) Real-time processing pipeline with <50ms end-to-end latency requirement. 3) Multi-layer safety system with hardware kill switches and software monitors. 4) Hybrid C++/Rust core for performance with Python prototyping layer. 5) Time-series database (InfluxDB/TimescaleDB) for neural pattern storage. 6) End-to-end encryption with neural signature authentication. 7) Support for 256 channels at 30kHz sampling rate. 8) Modular hardware abstraction layer for multiple BCI devices. 9) Bidirectional communication with feedback encoding. 10) GPU acceleration for neural decoding. 11) Kubernetes-based deployment for production scalability. 12) Comprehensive safety monitoring with emergency shutoff in <5ms.", "namespace": "default", "timestamp": 1752018181435}, {"key": "design/architecture/tech-stack", "value": "Technical Stack: OS: Real-time Linux with PREEMPT_RT. Languages: C++17/20 for signal processing, Rust for memory-safe components, CUDA for GPU acceleration, Python for prototypes. Databases: InfluxDB for time-series neural data, PostgreSQL for patterns, Redis for caching. Communication: gRPC internal, WebRTC streaming, custom TCP for hive. Key Libraries: Eigen for linear algebra, TensorFlow C++ API for ML, OpenCL for cross-platform GPU.", "namespace": "default", "timestamp": 1752018192664}, {"key": "architecture/decisions", "value": "{\"module\": \"shared-memory\", \"decisions\": [\"Use SQLite with better-sqlite3 for persistence\", \"Create unified module supporting both .swarm/ and .hive-mind/ directories\", \"Implement base SharedMemory class with core methods\", \"Extend with SwarmMemory for MCP-specific needs\", \"Design flexible schema with migration support\", \"Optimize for performance with caching and pooling\"]}", "namespace": "default", "timestamp": 1752116024819}, {"key": "test-key", "value": "Test value for persistence", "namespace": "default", "timestamp": 1752116037136}, {"key": "restart-test", "value": "Value before restart", "namespace": "default", "timestamp": 1752116074406}, {"key": "integration/architecture-found", "value": "Found SharedMemory module at src/memory/shared-memory.js created by Architecture Agent", "namespace": "default", "timestamp": 1752116118573}, {"key": "integration/mcp-update-decision", "value": "Updating MCP server to use SharedMemory class from src/memory/shared-memory.js for memory_usage tool implementation", "namespace": "default", "timestamp": 1752116136755}, {"key": "integration/completed-tasks", "value": "Integrated SharedMemory into MCP server, updated init command to create .swarm directory and initialize SQLite database, incremented version to 1.0.44", "namespace": "default", "timestamp": 1752116309680}, {"key": "testing/complete", "value": "Testing Agent has completed comprehensive testing. Results: Local persistence PASSED, NPM partially passed, <PERSON><PERSON> skipped, Regression PASSED. Full report at test-results/memory-persistence-test-report.md", "namespace": "default", "timestamp": 1752116402654}, {"key": "architecture/memory-module-completed", "value": "{\"status\": \"completed\", \"files_created\": [\"shared-memory.js\", \"swarm-memory.js\", \"index.js\", \"migration.js\", \"test-example.js\", \"README.md\"], \"features\": [\"SQLite persistence\", \"LRU caching\", \"Migration support\", \"TTL support\", \"Swarm-specific features\", \"MCP integration\"], \"timestamp\": \"2025-07-10T03:00:19Z\"}", "namespace": "default", "timestamp": 1752116421782}]}