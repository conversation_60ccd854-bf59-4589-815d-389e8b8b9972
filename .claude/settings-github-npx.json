{"env": {"CLAUDE_FLOW_AUTO_COMMIT": "false", "CLAUDE_FLOW_AUTO_PUSH": "false", "CLAUDE_FLOW_HOOKS_ENABLED": "true", "CLAUDE_FLOW_TELEMETRY_ENABLED": "true", "CLAUDE_FLOW_REMOTE_EXECUTION": "true", "CLAUDE_FLOW_GITHUB_INTEGRATION": "true", "CLAUDE_FLOW_CHECKPOINTS_ENABLED": "true", "CREATE_GH_RELEASE": "true"}, "permissions": {"allow": ["Bash(npx claude-flow *)", "Bash(npm run lint)", "Bash(npm run test:*)", "Bash(npm test *)", "Bash(git status)", "<PERSON><PERSON>(git diff *)", "Bash(git log *)", "Bash(git add *)", "<PERSON><PERSON>(git commit *)", "<PERSON><PERSON>(git push)", "<PERSON>sh(git config *)", "Bash(git tag *)", "<PERSON><PERSON>(git branch *)", "<PERSON><PERSON>(git checkout *)", "<PERSON>sh(git stash *)", "<PERSON>sh(git reset *)", "Bash(git rev-parse *)", "Bash(git ls-files *)", "Bash(gh *)", "<PERSON><PERSON>(node *)", "Ba<PERSON>(which *)", "Bash(pwd)", "Bash(ls *)", "<PERSON><PERSON>(jq *)", "<PERSON><PERSON>(test *)", "<PERSON><PERSON>(find *)", "<PERSON><PERSON>(grep *)", "<PERSON><PERSON>(sed *)", "Bash(awk *)", "<PERSON><PERSON>(curl *)", "<PERSON><PERSON>(mkdir *)", "Bash(cd *)", "<PERSON><PERSON>(cat *)", "<PERSON><PERSON>(echo *)", "Bash(npx claude-flow@alpha *)", "Bash(./claude-flow *)", "Bash(./.claude/helpers/*)"], "deny": ["Bash(rm -rf /)", "Bash(curl * | bash)", "Bash(wget * | sh)", "<PERSON><PERSON>(eval *)"]}, "hooks": {"PreToolUse": [{"matcher": "<PERSON><PERSON>", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.command // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks pre-command --command '{}' --validate-safety true --prepare-resources true"}]}, {"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.file_path // .tool_input.path // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks pre-edit --file '{}' --auto-assign-agents true --load-context true"}, {"type": "command", "command": "/bin/bash -c 'FILE=$(cat | jq -r \".tool_input.file_path // .tool_input.path // empty\"); if [ -n \"$FILE\" ]; then CHECKPOINT_BRANCH=\"checkpoint/pre-edit-$(date +%Y%m%d-%H%M%S)\"; git add -A && git stash push -m \"Pre-edit checkpoint for $FILE\" >/dev/null 2>&1 && git branch \"$CHECKPOINT_BRANCH\" && mkdir -p .claude/checkpoints && echo \"{\\\"branch\\\": \\\"$CHECKPOINT_BRANCH\\\", \\\"file\\\": \\\"$FILE\\\", \\\"timestamp\\\": \\\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\\\", \\\"type\\\": \\\"pre-edit\\\", \\\"original_branch\\\": \\\"$(git branch --show-current)\\\"}\" > \".claude/checkpoints/$(date +%s).json\" && git stash pop --quiet >/dev/null 2>&1 || true && echo \"✅ Created checkpoint: $CHECKPOINT_BRANCH for $FILE\"; fi'"}]}], "PostToolUse": [{"matcher": "<PERSON><PERSON>", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.command // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks post-command --command '{}' --track-metrics true --store-results true"}]}, {"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.file_path // .tool_input.path // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks post-edit --file '{}' --format true --update-memory true"}, {"type": "command", "command": "/bin/bash -c 'FILE=$(cat | jq -r \".tool_input.file_path // .tool_input.path // empty\"); if [ -n \"$FILE\" ] && [ -f \"$FILE\" ]; then if ! git ls-files --error-unmatch \"$FILE\" >/dev/null 2>&1; then git add \"$FILE\"; fi; if ! (git diff --cached --quiet \"$FILE\" 2>/dev/null && git diff --quiet \"$FILE\" 2>/dev/null); then TAG_NAME=\"checkpoint-$(date +%Y%m%d-%H%M%S)\"; BRANCH=$(git branch --show-current); git add \"$FILE\" && git commit -m \"🔖 Checkpoint: Edit $FILE\" --quiet && git tag -a \"$TAG_NAME\" -m \"Checkpoint after editing $FILE\" && mkdir -p .claude/checkpoints && DIFF_STATS=$(git diff HEAD~1 --stat | tr \"\\n\" \" \" | sed \"s/\\\"/\\\\\\\\\\\"/g\") && echo \"{\\\"tag\\\": \\\"$TAG_NAME\\\", \\\"file\\\": \\\"$FILE\\\", \\\"timestamp\\\": \\\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\\\", \\\"type\\\": \\\"post-edit\\\", \\\"branch\\\": \\\"$BRANCH\\\", \\\"diff_summary\\\": \\\"$DIFF_STATS\\\"}\" > \".claude/checkpoints/$(date +%s).json\" && echo \"✅ Created checkpoint: $TAG_NAME for $FILE\"; else echo \"ℹ️  No changes to checkpoint for $FILE\"; fi; fi'"}]}], "UserPromptSubmit": [{"hooks": [{"type": "command", "command": "/bin/bash -c 'TASK=$(echo \"{{user_prompt}}\" | head -c 100 | tr \"\\n\" \" \"); if [ -n \"$TASK\" ]; then CHECKPOINT_NAME=\"task-$(date +%Y%m%d-%H%M%S)\"; git add -A && git commit -m \"🔖 Task checkpoint: $TASK...\" --quiet || true; if command -v gh &> /dev/null; then echo \"🚀 Creating GitHub release for checkpoint...\"; gh release create \"$CHECKPOINT_NAME\" --title \"Checkpoint: $(date +\"%Y-%m-%d %H:%M\")\" --notes \"Task: $TASK\n\n## Checkpoint Details\n- Branch: $(git branch --show-current)\n- Commit: $(git rev-parse HEAD)\n- Files changed: $(git diff HEAD~1 --stat 2>/dev/null | wc -l || echo 0) files\n\n## Rollback Instructions\n\\`\\`\\`bash\n# To rollback to this checkpoint:\ngit checkout $CHECKPOINT_NAME\n\\`\\`\\`\" --prerelease || echo \"⚠️  Failed to create GitHub release\"; fi; mkdir -p .claude/checkpoints && echo \"{\\\"checkpoint\\\": \\\"$CHECKPOINT_NAME\\\", \\\"task\\\": \\\"$TASK\\\", \\\"timestamp\\\": \\\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\\\", \\\"commit\\\": \\\"$(git rev-parse HEAD)\\\", \\\"github_release\\\": \\\"$(command -v gh &> /dev/null && echo true || echo false)\\\"}\" > \".claude/checkpoints/task-$(date +%s).json\" && echo \"✅ Created task checkpoint: $CHECKPOINT_NAME\"; fi'"}]}], "Stop": [{"hooks": [{"type": "command", "command": "npx claude-flow@alpha hooks session-end --generate-summary true --persist-state true --export-metrics true"}, {"type": "command", "command": "/bin/bash -c 'SESSION_ID=\"session-$(date +%Y%m%d-%H%M%S)\"; SUMMARY_FILE=\".claude/checkpoints/summary-$SESSION_ID.md\"; mkdir -p .claude/checkpoints && echo \"# Session Summary - $(date +\"%Y-%m-%d %H:%M:%S\")\n\n## Checkpoints Created\n$(find .claude/checkpoints -name \"*.json\" -mtime -1 -exec basename {} \\; | sort)\n\n## Files Modified\n$(git diff --name-only $(git log --format=%H -n 1 --before=\"1 hour ago\" 2>/dev/null) 2>/dev/null || echo \"No files tracked\")\n\n## Recent Commits\n$(git log --oneline -10 --grep=\"Checkpoint\" || echo \"No checkpoint commits\")\n\n## GitHub Releases Created\n$(gh release list --limit 10 | grep \"checkpoint-\" || echo \"No GitHub releases\")\n\n## Rollback Instructions\nTo rollback to a specific checkpoint:\n\\`\\`\\`bash\n# List all checkpoints\ngit tag -l \"checkpoint-*\" | sort -r\n\n# List GitHub releases\ngh release list\n\n# Rollback to a checkpoint\ngit checkout checkpoint-YYYYMMDD-HHMMSS\n\\`\\`\\`\" > \"$SUMMARY_FILE\" && git add -A && git commit -m \"🏁 Session end checkpoint: $SESSION_ID\" --quiet || true && git tag -a \"session-end-$SESSION_ID\" -m \"End of Claude session\" && if command -v gh &> /dev/null; then echo \"📊 Creating GitHub session summary...\"; gh release create \"session-$SESSION_ID\" --title \"Session Summary: $(date +\"%Y-%m-%d %H:%M\")\" --notes-file \"$SUMMARY_FILE\" --prerelease || echo \"⚠️  Failed to create GitHub session summary\"; fi && echo \"✅ Session summary saved to: $SUMMARY_FILE\" && echo \"📌 Final checkpoint: session-end-$SESSION_ID\"'"}]}], "PreCompact": [{"matcher": "manual", "hooks": [{"type": "command", "command": "/bin/bash -c 'INPUT=$(cat); CUSTOM=$(echo \"$INPUT\" | jq -r \".custom_instructions // \"\"\"); echo \"🔄 PreCompact Guidance:\"; echo \"📋 IMPORTANT: Review CLAUDE.md in project root for:\"; echo \"   • 54 available agents and concurrent usage patterns\"; echo \"   • Swarm coordination strategies (hierarchical, mesh, adaptive)\"; echo \"   • SPARC methodology workflows with batchtools optimization\"; echo \"   • Critical concurrent execution rules (GOLDEN RULE: 1 MESSAGE = ALL OPERATIONS)\"; if [ -n \"$CUSTOM\" ]; then echo \"🎯 Custom compact instructions: $CUSTOM\"; fi; echo \"✅ Ready for compact operation\"'"}]}, {"matcher": "auto", "hooks": [{"type": "command", "command": "/bin/bash -c 'echo \"🔄 Auto-Compact Guidance (Context Window Full):\"; echo \"📋 CRITICAL: Before compacting, ensure you understand:\"; echo \"   • All 54 agents available in .claude/agents/ directory\"; echo \"   • Concurrent execution patterns from CLAUDE.md\"; echo \"   • Batchtools optimization for 300% performance gains\"; echo \"   • Swarm coordination strategies for complex tasks\"; echo \"⚡ Apply GOLDEN RULE: Always batch operations in single messages\"; echo \"✅ Auto-compact proceeding with full agent context\"'"}]}]}, "includeCoAuthoredBy": true, "enabledMcpjsonServers": ["claude-flow", "ruv-swarm"]}