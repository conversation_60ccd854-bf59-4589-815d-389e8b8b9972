name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '20'

jobs:
  # Code quality and security checks
  security:
    name: Security & Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: |
          npm audit --audit-level=high
          npm audit --production --audit-level=moderate
        
      - name: Lint code
        run: npm run lint

      - name: Type check
        run: npm run typecheck

      - name: Check for outdated dependencies
        run: npm outdated || true
        continue-on-error: true

      - name: License compliance check
        run: npx license-checker --onlyAllow 'MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;CC0-1.0' || true
        continue-on-error: true

  # All tests
  test:
    name: Test Suite
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run all tests  
        run: npm test

      - name: Generate coverage report
        if: matrix.os == 'ubuntu-latest'
        run: npm run test:coverage

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.os }}
          path: coverage/

  # Documentation generation
  docs:
    name: Documentation & Examples
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Check documentation
        run: |
          echo "✅ Documentation check passed"
          ls -la README.md CHANGELOG.md

  # Build and package
  build:
    name: Build & Package
    runs-on: ubuntu-latest
    needs: [security, test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: |
          echo "Building project..."
          npm run build:ts

      - name: Test CLI binary
        run: |
          chmod +x ./bin/claude-flow
          ./bin/claude-flow --version

      - name: Package build
        run: |
          npm pack
          ls -la *.tgz

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/
            bin/
            *.tgz

  # Deployment (only on main branch)
  deploy:
    name: Deploy & Release
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Prepare for deployment
        run: |
          echo "✅ Ready for deployment"
          echo "Version: $(node -p "require('./package.json').version")"

  # Final status check
  status:
    name: CI Status
    runs-on: ubuntu-latest
    needs: [security, test, build]
    if: always()
    
    steps:
      - name: Check overall status
        run: |
          echo "✅ CI Pipeline completed"
          echo "Security: ${{ needs.security.result }}"
          echo "Test: ${{ needs.test.result }}"
          echo "Build: ${{ needs.build.result }}"