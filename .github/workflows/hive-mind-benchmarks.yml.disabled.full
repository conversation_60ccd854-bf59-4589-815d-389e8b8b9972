name: Hive Mind Benchmarks

on:
  push:
    branches: [ main, develop, claude-flow-v2.0.0 ]
    paths:
      - 'src/**'
      - 'benchmark/**'
      - '.github/workflows/hive-mind-benchmarks.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/**'
      - 'benchmark/**'
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      profile:
        description: 'Benchmark profile to run'
        required: true
        default: 'quick'
        type: choice
        options:
          - quick
          - standard
          - comprehensive
          - stress
      timeout_minutes:
        description: 'Timeout in minutes'
        required: false
        default: '60'
        type: string
      parallel_workers:
        description: 'Number of parallel workers'
        required: false
        default: '2'
        type: string

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.9'
  BENCHMARK_PROFILE: ${{ github.event.inputs.profile || 'quick' }}
  BENCHMARK_TIMEOUT: ${{ github.event.inputs.timeout_minutes || '60' }}
  PARALLEL_WORKERS: ${{ github.event.inputs.parallel_workers || '2' }}

jobs:
  validate-environment:
    name: Validate Test Environment
    runs-on: ubuntu-latest
    outputs:
      should-run-benchmarks: ${{ steps.check.outputs.should-run }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          npm ci
          pip install -r benchmark/hive-mind-benchmarks/requirements.txt

      - name: Check benchmark runner
        id: check
        run: |
          cd benchmark/hive-mind-benchmarks
          python3 benchmark_runner.py --help
          python3 scripts/automated_test_runner.py --help
          echo "should-run=true" >> $GITHUB_OUTPUT

      - name: Validate configuration
        run: |
          cd benchmark/hive-mind-benchmarks
          python3 -c "import json; json.load(open('config/test-config.json'))"
          echo "✅ Configuration validation passed"

  quick-benchmarks:
    name: Quick Benchmarks (PR)
    runs-on: ubuntu-latest
    needs: validate-environment
    if: github.event_name == 'pull_request' && needs.validate-environment.outputs.should-run-benchmarks == 'true'
    timeout-minutes: 15
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          npm ci
          pip install -r benchmark/hive-mind-benchmarks/requirements.txt

      - name: Run quick benchmarks
        run: |
          cd benchmark/hive-mind-benchmarks
          python3 scripts/automated_test_runner.py \
            --profile quick \
            --timeout 10 \
            --output-dir pr-benchmark-results \
            --quiet
        env:
          LOG_LEVEL: WARNING

      - name: Upload PR benchmark results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: pr-benchmark-results
          path: benchmark/hive-mind-benchmarks/pr-benchmark-results/
          retention-days: 7

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = 'benchmark/hive-mind-benchmarks/pr-benchmark-results';
            
            if (fs.existsSync(path)) {
              const files = fs.readdirSync(path);
              const reportFile = files.find(f => f.includes('automation_report'));
              
              if (reportFile) {
                const report = JSON.parse(fs.readFileSync(`${path}/${reportFile}`, 'utf8'));
                const summary = report.test_summary;
                const thresholds = report.performance_validation;
                
                const comment = `## 🐝 Hive Mind Benchmark Results
                
                **Test Summary:**
                - Total tests: ${summary.completed_tests}/${summary.total_configurations}
                - Success rate: ${((summary.successful_tests / summary.completed_tests) * 100).toFixed(1)}%
                - Performance thresholds: ${thresholds.thresholds_met ? '✅ Met' : '❌ Failed'}
                
                ${thresholds.violations.length > 0 ? `
                **Performance Issues:**
                ${thresholds.violations.slice(0, 3).map(v => `- ${v}`).join('\n')}
                ` : ''}
                
                <details>
                <summary>View detailed results</summary>
                
                \`\`\`json
                ${JSON.stringify(summary, null, 2)}
                \`\`\`
                </details>`;
                
                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            }

  standard-benchmarks:
    name: Standard Benchmarks
    runs-on: ubuntu-latest
    needs: validate-environment
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || github.event_name == 'workflow_dispatch'
    timeout-minutes: 45
    strategy:
      matrix:
        profile: [quick, standard]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          npm ci
          pip install -r benchmark/hive-mind-benchmarks/requirements.txt

      - name: Run ${{ matrix.profile }} benchmarks
        run: |
          cd benchmark/hive-mind-benchmarks
          python3 scripts/automated_test_runner.py \
            --profile ${{ matrix.profile }} \
            --timeout ${{ env.BENCHMARK_TIMEOUT }} \
            --output-dir ${{ matrix.profile }}-benchmark-results
        env:
          PARALLEL_WORKERS: ${{ env.PARALLEL_WORKERS }}

      - name: Generate performance report
        if: always()
        run: |
          cd benchmark/hive-mind-benchmarks
          if [ -d "${{ matrix.profile }}-benchmark-results" ]; then
            python3 -c "
            import json, os
            from pathlib import Path
            
            results_dir = Path('${{ matrix.profile }}-benchmark-results')
            if results_dir.exists():
                files = list(results_dir.glob('automation_report_*.json'))
                if files:
                    with open(files[0]) as f:
                        report = json.load(f)
                    
                    # Create GitHub step summary
                    summary = report.get('test_summary', {})
                    validation = report.get('performance_validation', {})
                    
                    with open(os.environ['GITHUB_STEP_SUMMARY'], 'w') as f:
                        f.write(f'''# 🐝 Hive Mind Benchmark Report (${{ matrix.profile }})
            
            ## Summary
            - **Total Tests:** {summary.get('completed_tests', 0)}/{summary.get('total_configurations', 0)}
            - **Success Rate:** {(summary.get('successful_tests', 0) / max(summary.get('completed_tests', 1), 1) * 100):.1f}%
            - **Performance Thresholds:** {'✅ Met' if validation.get('thresholds_met', False) else '❌ Failed'}
            
            ## Performance Metrics
            {chr(10).join(f'- {k}: {v}' for k, v in report.get('benchmark_analysis', {}).get('summary', {}).items())}
            
            {'## ⚠️ Violations' + chr(10) + chr(10).join(f'- {v}' for v in validation.get('violations', [])) if validation.get('violations') else ''}
            ''')  
            "
          fi

      - name: Upload benchmark results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: ${{ matrix.profile }}-benchmark-results
          path: benchmark/hive-mind-benchmarks/${{ matrix.profile }}-benchmark-results/
          retention-days: 30

  comprehensive-benchmarks:
    name: Comprehensive Benchmarks
    runs-on: ubuntu-latest
    needs: validate-environment
    if: github.event_name == 'schedule' || (github.event_name == 'workflow_dispatch' && github.event.inputs.profile == 'comprehensive')
    timeout-minutes: 180
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          npm ci
          pip install -r benchmark/hive-mind-benchmarks/requirements.txt

      - name: Run comprehensive benchmarks
        run: |
          cd benchmark/hive-mind-benchmarks
          python3 scripts/automated_test_runner.py \
            --profile comprehensive \
            --timeout 150 \
            --output-dir comprehensive-benchmark-results
        env:
          PARALLEL_WORKERS: 4

      - name: Generate comprehensive report
        if: always()
        run: |
          cd benchmark/hive-mind-benchmarks
          python3 -c "
          import json, glob, pandas as pd, matplotlib.pyplot as plt
          from pathlib import Path
          
          # Load and process results
          results_dir = Path('comprehensive-benchmark-results')
          if results_dir.exists():
              csv_files = list(results_dir.glob('summary_*.csv'))
              if csv_files:
                  df = pd.read_csv(csv_files[0])
                  
                  # Generate performance plots
                  fig, axes = plt.subplots(2, 2, figsize=(15, 10))
                  
                  # Coordination latency by topology
                  df.groupby('topology')['coord_latency'].mean().plot(kind='bar', ax=axes[0,0])
                  axes[0,0].set_title('Avg Coordination Latency by Topology')
                  axes[0,0].set_ylabel('Latency (ms)')
                  
                  # Memory usage by agent count
                  df.plot.scatter(x='agent_count', y='memory_mb', ax=axes[0,1])
                  axes[0,1].set_title('Memory Usage vs Agent Count')
                  
                  # Success rate by coordination type
                  success_rate = df.groupby('coordination')['success'].mean()
                  success_rate.plot(kind='bar', ax=axes[1,0])
                  axes[1,0].set_title('Success Rate by Coordination Type')
                  axes[1,0].set_ylabel('Success Rate')
                  
                  # Initialization time distribution
                  df['init_time'].hist(bins=20, ax=axes[1,1])
                  axes[1,1].set_title('Initialization Time Distribution')
                  axes[1,1].set_xlabel('Time (seconds)')
                  
                  plt.tight_layout()
                  plt.savefig(results_dir / 'performance_analysis.png', dpi=300, bbox_inches='tight')
                  print('Performance analysis plots generated')
          "

      - name: Upload comprehensive results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: comprehensive-benchmark-results
          path: benchmark/hive-mind-benchmarks/comprehensive-benchmark-results/
          retention-days: 90

  docker-benchmarks:
    name: Docker Container Benchmarks
    runs-on: ubuntu-latest
    needs: validate-environment
    if: github.event_name == 'workflow_dispatch' || github.event_name == 'schedule'
    timeout-minutes: 60
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build benchmark container
        run: |
          cd benchmark/hive-mind-benchmarks
          docker build -f docker/Dockerfile -t hive-mind-benchmark:latest .

      - name: Run containerized benchmarks
        run: |
          docker run --rm \
            -v $(pwd)/docker-benchmark-results:/app/benchmark-results \
            -e BENCHMARK_PROFILE=standard \
            -e BENCHMARK_TIMEOUT_MINUTES=45 \
            -e LOG_LEVEL=INFO \
            hive-mind-benchmark:latest \
            python3 scripts/automated_test_runner.py \
              --profile standard \
              --timeout 45 \
              --output-dir /app/benchmark-results

      - name: Upload Docker benchmark results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: docker-benchmark-results
          path: docker-benchmark-results/
          retention-days: 30

  performance-regression:
    name: Performance Regression Analysis
    runs-on: ubuntu-latest
    needs: [standard-benchmarks]
    if: always() && needs.standard-benchmarks.result == 'success'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Download current results
        uses: actions/download-artifact@v3
        with:
          name: standard-benchmark-results
          path: current-results/

      - name: Download baseline results
        continue-on-error: true
        run: |
          # Try to get baseline from main branch artifact
          gh run list --workflow="hive-mind-benchmarks.yml" --branch=main --status=success --limit=5 --json databaseId,headSha \
            | jq -r '.[].databaseId' | head -1 | xargs -I {} \
            gh run download {} --name standard-benchmark-results --dir baseline-results/ || true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Analyze performance regression
        run: |
          pip install pandas numpy matplotlib
          python3 -c "
          import json, pandas as pd, numpy as np
          from pathlib import Path
          
          def load_results(results_dir):
              results_path = Path(results_dir)
              if not results_path.exists():
                  return None
              csv_files = list(results_path.glob('summary_*.csv'))
              return pd.read_csv(csv_files[0]) if csv_files else None
          
          current = load_results('current-results')
          baseline = load_results('baseline-results')
          
          if current is not None and baseline is not None:
              # Compare key metrics
              metrics = ['init_time', 'coord_latency', 'memory_mb']
              regression_threshold = 1.1  # 10% regression threshold
              
              regressions = []
              for metric in metrics:
                  current_avg = current[metric].mean()
                  baseline_avg = baseline[metric].mean()
                  ratio = current_avg / baseline_avg if baseline_avg > 0 else 1
                  
                  if ratio > regression_threshold:
                      regressions.append(f'{metric}: {ratio:.2f}x increase ({current_avg:.2f} vs {baseline_avg:.2f})')
              
              if regressions:
                  print('Performance regressions detected:')
                  for regression in regressions:
                      print(f'  - {regression}')
                  exit(1)
              else:
                  print('No significant performance regressions detected')
          else:
              print('Could not perform regression analysis - missing data')
          "

  notify-results:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [quick-benchmarks, standard-benchmarks, comprehensive-benchmarks, docker-benchmarks]
    if: always() && (needs.standard-benchmarks.result == 'failure' || needs.comprehensive-benchmarks.result == 'failure')
    steps:
      - name: Notify on benchmark failures
        run: |
          echo "⚠️ Hive Mind benchmark failures detected:"
          echo "- Quick: ${{ needs.quick-benchmarks.result }}"
          echo "- Standard: ${{ needs.standard-benchmarks.result }}"
          echo "- Comprehensive: ${{ needs.comprehensive-benchmarks.result }}"
          echo "- Docker: ${{ needs.docker-benchmarks.result }}"
          
          # Here you could add Slack/Discord/email notifications
          # Example: curl -X POST -H 'Content-type: application/json' --data '{"text":"Benchmark failures detected!"}' $SLACK_WEBHOOK_URL