# Test container for claude-flow feature system
# Tests across multiple Node.js versions and platforms

# Base stage for common dependencies
FROM node:18-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --production

# Development dependencies stage
FROM base AS dev-deps
RUN npm ci

# Test stage
FROM dev-deps AS test
COPY . .

# Run tests
RUN npm run typecheck
RUN npm run test:features:coverage
RUN npm run build

# Simulate npx execution
FROM node:18-alpine AS npx-test
RUN npm install -g claude-flow@latest || echo "Package not yet published"
WORKDIR /test
COPY --from=test /app/dist ./dist
COPY --from=test /app/package.json ./

# Run basic validation
CMD ["node", "-e", "console.log('NPX simulation complete')"]

# Multi-platform build test
FROM --platform=$BUILDPLATFORM node:18-alpine AS platform-test
ARG TARGETPLATFORM
ARG BUILDPLATFORM
RUN echo "Building for $TARGETPLATFORM on $BUILDPLATFORM"
WORKDIR /app
COPY --from=test /app/dist ./dist
RUN node -v && npm -v

# Final validation stage
FROM alpine:latest AS final
RUN apk add --no-cache nodejs npm
COPY --from=test /app/dist /app/dist
COPY --from=test /app/package.json /app/
WORKDIR /app
RUN echo "Feature system test container ready"